using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace PlayerFAP.Components.Detection
{
    // Helper class to find the singleton entity from MonoBehaviour
    public class DetectionTargetSingleton : MonoBehaviour
    {
        private World defaultWorld;
        private EntityManager entityManager;
        private EntityQuery singletonQuery;
        private float lastUpdateTime;
        private const float UPDATE_INTERVAL = 0.1f; // 100ms update interval for stability

        private void Awake()
        {
            defaultWorld = World.DefaultGameObjectInjectionWorld;
            entityManager = defaultWorld.EntityManager;
            
            // Query for our singleton
            singletonQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<DetectionTargetComponent>(),
                ComponentType.ReadOnly<DetectedEnemyBuffer>()
            );
        }

        public (Entity currentTarget, Vector3 targetPosition, bool hasTarget, bool isInFOV, float lastKnownDistance, float score) GetCurrentTarget()
        {
            if (singletonQuery.IsEmpty) 
                return (Entity.Null, Vector3.zero, false, false, 0f,0f);

            var targetComponent = entityManager.GetComponentData<DetectionTargetComponent>(singletonQuery.GetSingletonEntity());
            var currentTime = Time.time;
            
            // Only update if enough time has passed
            if (currentTime - lastUpdateTime >= UPDATE_INTERVAL)
            {
                lastUpdateTime = currentTime;
            }

            return (targetComponent.CurrentTarget, 
                   new Vector3(targetComponent.CurrentPosition.x, targetComponent.CurrentPosition.y, targetComponent.CurrentPosition.z),
                   targetComponent.HasTarget,
                   targetComponent.IsInFOV,
                   math.length(targetComponent.CurrentPosition - targetComponent.LastKnownPosition),targetComponent.Score);
        }

        public DetectedEnemy[] GetDetectedEnemies(float3 playerPosition)
        {
            if (singletonQuery.IsEmpty)
            {
                UnityEngine.Debug.Log("[DetectionTargetSingleton.GetDetectedEnemies] singletonQuery is Empty. No DetectionTargetComponent found.");
                return new DetectedEnemy[0];
            }

            UnityEngine.Debug.Log("[DetectionTargetSingleton.GetDetectedEnemies] singletonQuery is NOT Empty. Attempting to get buffer.");
            var singletonEntity = singletonQuery.GetSingletonEntity();
            if (!entityManager.HasBuffer<DetectedEnemyBuffer>(singletonEntity))
            {
                UnityEngine.Debug.LogWarning("[DetectionTargetSingleton.GetDetectedEnemies] Singleton entity does NOT have DetectedEnemyBuffer.");
                return new DetectedEnemy[0];
            }

            var buffer = entityManager.GetBuffer<DetectedEnemyBuffer>(singletonEntity);
            UnityEngine.Debug.Log($"[DetectionTargetSingleton.GetDetectedEnemies] DetectedEnemyBuffer length: {buffer.Length}");
            var enemies = new DetectedEnemy[buffer.Length];
            
            for (int i = 0; i < buffer.Length; i++)
            {
                var enemy = buffer[i];
                enemies[i] = new DetectedEnemy
                {
                    Entity = enemy.Entity,
                    Position = new Vector3(enemy.Position.x, enemy.Position.y, enemy.Position.z),
                    Distance = math.length(enemy.Position - playerPosition), // Keep this for compatibility
                    IsInFOV = enemy.IsInFOV,
                    Score = enemy.Score

                };
            }

            return enemies;
        }

        public int GetDetectedEnemyCount()
        {
            if (singletonQuery.IsEmpty) 
                return 0;

            var buffer = entityManager.GetBuffer<DetectedEnemyBuffer>(singletonQuery.GetSingletonEntity());
            return buffer.Length;
        }
    }

    public struct DetectedEnemy
    {
        public Entity Entity;
        public Vector3 Position;
        public float Distance;
        public bool IsInFOV;
        public float Score;
    }
}
