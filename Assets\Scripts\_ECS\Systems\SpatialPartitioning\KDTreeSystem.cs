using GimmeDOTSGeometry;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Transforms;
using PlayerFAP.Components;
using PlayerFAP.Components.SpatialPartitioning;
using Position3D = GimmeDOTSGeometry.Position3D;

namespace PlayerFAP.Systems.SpatialPartitioning
{
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(SpatialHashGridSystem))]
    public partial struct KDTreeSystem : ISystem
    {
        private EntityQuery m_EnemyQuery;
        private EntityQuery m_TreeQuery;
        private EntityQuery m_SensorDetector;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<SphereDetectSensorComponent>();
            var builder = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<EnemyTag, LocalToWorld>();
            m_EnemyQuery = state.GetEntityQuery(builder);

            builder = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<KDTreeComponent>();
            m_TreeQuery = state.GetEntityQuery(builder);

            if (m_TreeQuery.IsEmpty)
            {
                CreateKDTreeEntity(ref state);
            }
        }

        private void CreateKDTreeEntity(ref SystemState state)
        {
            var treeEntity = state.EntityManager.CreateEntity();
            state.EntityManager.AddComponent<KDTreeComponent>(treeEntity);
            state.EntityManager.AddBuffer<KDTreeSearchResult>(treeEntity);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (m_EnemyQuery.IsEmpty)
                return;

            var sensor = state.EntityManager.GetComponentData<SphereDetectSensorComponent>(SystemAPI.GetSingletonEntity<SphereDetectSensorComponent>());
            
            // Only update if KDTree is enabled (support both legacy and new detection method)
            bool useKDTree = sensor.UseKDTree || sensor.DetectionMethod == DetectionMethod.KDTree || sensor.DetectionMethod == DetectionMethod.Hybrid;
            if (!useKDTree)
                return;

            var treeEntity = m_TreeQuery.GetSingletonEntity();
            var treeComponent = state.EntityManager.GetComponentData<KDTreeComponent>(treeEntity);
            var searchResults = state.EntityManager.GetBuffer<KDTreeSearchResult>(treeEntity);
        
            var enemies = m_EnemyQuery.ToEntityArray(Allocator.TempJob);
            var positions = m_EnemyQuery.ToComponentDataArray<LocalToWorld>(Allocator.TempJob);

            // Initialize or update KDTree
            if (!treeComponent.IsInitialized)
            {
                var points = new NativeArray<Position3D>(enemies.Length, Allocator.TempJob);
                for (int i = 0; i < enemies.Length; i++)
                {
                    points[i] = new Position3D() { Position = positions[i].Position };
                }

                treeComponent.Tree = new Native3DKDTree<Position3D>(points, Allocator.Persistent);
                treeComponent.IsInitialized = true;
                treeComponent.SearchRadius = sensor.DetectionRadius; // Adjust based on your needs
                state.EntityManager.SetComponentData(treeEntity, treeComponent);
                points.Dispose();
            }
            else
            {
                // Create a new array for updated positions
                var points = new NativeArray<Position3D>(enemies.Length, Allocator.TempJob);
                for (int i = 0; i < enemies.Length; i++)
                {
                    points[i] = new Position3D() { Position = positions[i].Position };
                }

                // Dispose old tree and create new one
                treeComponent.Tree.Dispose();
                treeComponent.Tree = new Native3DKDTree<Position3D>(points, Allocator.Persistent);
                state.EntityManager.SetComponentData(treeEntity, treeComponent);
                
                points.Dispose();
            }

            enemies.Dispose();
            positions.Dispose();
        }

        [BurstCompile]
        public void OnDestroy(ref SystemState state)
        {
            if (!m_TreeQuery.IsEmpty)
            {
                var treeEntity = m_TreeQuery.GetSingletonEntity();
                var treeComponent = state.EntityManager.GetComponentData<KDTreeComponent>(treeEntity);
                if (treeComponent.IsInitialized)
                {
                    treeComponent.Tree.Dispose();
                }
            }
        }
    }
}
