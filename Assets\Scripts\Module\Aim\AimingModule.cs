using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DefaultNamespace.Mono.Interface;
using DG.Tweening;
using Events;
using Module.Mono.Animancer.RealsticFemale.Sensors;
using PlayerFAP.Components.Detection;
using PlayerFAP.Tags;
using RootMotion.Demos;
using RootMotion.FinalIK;
using Sirenix.OdinInspector;
using SubModule.Aiming;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEditor;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public class AimingModule : SerializedMonoBehaviour, IAimingModule
        , INeedSubModule<WeaponSubModuleState, SubModule.Aiming.AimingSubModule>
        , INeedInput<CharacterParameter>
    {
        [field: SerializeField] public bool CanUpdate { get; set; }
        [field: SerializeField] public int ControllerIndex { get; private set; }

        [SerializeField] private List<MainState> mainState;

        List<MainState> IModule.MainState => MainState;
        public List<MainState> MainState => mainState;
        [SerializeField] private WeaponSubState subState;
        [field: SerializeField] public WeaponSubModuleState SubModuleState { get; private set; }

        [field: SerializeField]
        public WeaponSubState SubState
        {
            get { return subState; }
            set
            {
                if (subState != value)
                {
                    subState = value;
                    //DebugLogManager.Instance.Log("SubState: " + subState);
                    PlayerController.Instance.StateManager.CurrentSubState = subState;
                }
            }
        }

        [field: SerializeField] public List<CharacterParameter> InputName { get; private set; }

        [SerializeField] private bool hasRefreshRate;
        [SerializeField] private float refreshRate;
        [SerializeField] private int executionPriority;

        public int ExecutionPriority => executionPriority;

        [SerializeField] private LookAtIK m_lookAtIK;
        [SerializeField] private RagdollAimingController ragdollAiming;

        private bool m_isAiming;

        [field: SerializeField]
        public bool IsAiming
        {
            get => m_isAiming;
            private set
            {
                if (m_isAiming != value)
                {
                    m_isAiming = value;
                    UpdateAimingState();
                }
            }
        }

        [SerializeField] private bool autoAiming;

        public bool HasRefreshRate => hasRefreshRate;
        public float RefreshRate => refreshRate;

        // Reference to the configuration scriptable object (following Rule #4)
        [SerializeField] private AimingConfiguration aimingConfig;

        // Cone parameters - these will be initialized from the configuration
        [SerializeField][ReadOnly] private float currentConeAngle; // Angle of the cone in degrees
        [SerializeField][ReadOnly] private float coneRange; // Range of the cone

        [SerializeField] private Transform playerTransform;
        [SerializeField] private Transform aimHelperTransform; // Reference to the spine transform

        public Transform AimHelperTransform => aimHelperTransform;

        // Reference to the target
        [SerializeField] private bool m_isForwardAiming = true;
        [SerializeField] private Transform m_aimTarget;
        [SerializeField] private Transform m_forwardAimTarget;
        [SerializeField] private bool m_targetDetected;

        private float3? m_target;
        [SerializeField] private string m_targetName;
        [SerializeField] private float3 m_lastTarget;
        [SerializeField] private bool m_isInFOV;
        [SerializeField] private float m_currentScore;

        private Tweener m_targetTween;

        // Target selection parameters are now read from the configuration (following Rule #5 - avoid redundant fields)

        public float ThresholdDistanceToCurrentTarget
        {
            get
            {
                if (!m_target.HasValue) return float.MaxValue;
                if (aimingConfig == null) return float.MaxValue;

                var currentTargetDistance = math.distance(playerTransform.position, m_target.Value);
                // Normalize the distance threshold based on current target's distance
                return (currentTargetDistance / aimingConfig.baseDistanceThreshold) * aimingConfig.distanceWeight;
            }
        }

        public float ThresholdAngleToCurrentTarget
        {
            get
            {
                if (!m_target.HasValue) return float.MaxValue;
                if (aimingConfig == null) return float.MaxValue;

                var directionToTarget = math.normalize(m_target.Value - (float3)playerTransform.position);
                var angleToTarget = math.degrees(math.acos(math.dot(directionToTarget, playerTransform.forward)));
                // Normalize the angle threshold based on current target's angle
                return (angleToTarget / aimingConfig.baseAngleThreshold) * aimingConfig.angleWeight;
            }
        }

        // Cache for performance
        private readonly Dictionary<Entity, Transform> m_entityTransformCache = new Dictionary<Entity, Transform>();
        private readonly HashSet<Entity> m_activeTargets = new HashSet<Entity>();

        [SerializeField] private DetectSensorBase m_detectSensorBase;

        [field: SerializeField]
        public Dictionary<WeaponSubModuleState, AimingSubModule> SubModules { get; private set; }

        [ShowInInspector] private Dictionary<string, float3> m_allDetectedTarget { get; set; }

        [SerializeField] private bool m_canSwitchTarget = false;

        [SerializeField] private CharacterParameters m_characterParameters;
        [SerializeField] private float m_closestTargetDistanceThreshold;
        [SerializeField] private bool m_isAnyTargetBehind;

        [SerializeField] private float _cooldownEndTime = -1f;
        private bool _isInCooldown = false;

        public Transform AimTarget => ragdollAiming.GetPointer();
        public float3 CurrentTarget => m_lastTarget;

        private float m_switchTargetTimer;
        private bool m_isSwitchingTarget;

        private DetectionTargetSingleton detectionTarget;

        [Serializable]
        private struct CachedEnemy
        {
            [SerializeField] public float3 Position;
            [SerializeField] public float Distance;

            [SerializeField] public bool IsInFOV;
            [SerializeField] public float Score;
            [SerializeField] public float ThresholdDistanceToCurrentTartget;
            [SerializeField] public float ThresholdAngleToCurrentTartget;
        }

        [SerializeField] private List<CachedEnemy> m_cachedDetectedEnemies = new List<CachedEnemy>();
        [SerializeField] private float3 m_cachedCurrentTargetPosition;
        [SerializeField] private bool m_hasCachedTarget;

        private World ecsWorld;
        private EntityQuery detectedQuery;
        private EntityQuery inFOVQuery;
        private EntityQuery currentTargetQuery;

        public void Initialize(int controllerIndex)
        {
            m_allDetectedTarget = new Dictionary<string, float3>(100); // Pre-allocate with expected capacity
            m_detectSensorBase.Initialize(this, onDetectTarget, onLostTarget);

            // Update ECS detection parameters
            UpdateECSDetectionParameters();
            CanUpdate = true;
        }

        public Enum GetModuleSubState() => subState;

        // Character rotation is now handled by MovementModule

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if (!enabled) return;

            if (!CanUpdate)
            {
                float currentTime = Time.time;
                float cooldownDuration = aimingConfig != null ? aimingConfig.CooldownAfterLostAllEnenies : 1.0f;
                
                if (_isInCooldown && currentTime >= _cooldownEndTime)
                {
                    // Cooldown has finished
                    CanUpdate = true;
                    _isInCooldown = false;
                    
                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        float actualDuration = currentTime - (_cooldownEndTime - cooldownDuration);
                        Debug.Log($"[AimingModule] Cooldown period ended. Actual duration: {actualDuration:F2}s (target: {cooldownDuration:F2}s)");
                    }
                }
                else if (_isInCooldown && aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    // Debug log to track cooldown progress
                    float remainingTime = _cooldownEndTime - currentTime;
                    float elapsedTime = cooldownDuration - remainingTime;
                    Debug.Log($"[AimingModule] Still in cooldown: {elapsedTime:F2}s / {cooldownDuration:F2}s (remaining: {remainingTime:F2}s)");
                }

                if (!CanUpdate)
                    return;
            }

            SubState = IsAiming ? WeaponSubState.Aiming : WeaponSubState.Idle;

            // Aiming logic here
            switch (SubState)
            {
                case WeaponSubState.Idle:
                    StopAiming();
                    break;
                case WeaponSubState.Aiming:
                    StartAiming();
                    break;
                case WeaponSubState.Shooting:
                    // Shooting logic
                    break;
            }

            //DebugLogManager.Instance.Log("UpdateModule1: " +SubState);
            // Update the aim target position based on the cone detection
            if (detectionTarget != null)
            {
                UpdateDetectionCache();
            }

            UpdateAimTargetPosition();

            // Update the AimTarget in CharacterParameters for MovementModule to use
            if (IsAiming && m_hasCachedTarget && m_target.HasValue)
            {
                m_characterParameters.SetAimTarget(m_target.Value);
            }

            OnSubModuleChange(
                (WeaponSubModuleState)PlayerController.Instance.StateManager.GetState(typeof(WeaponSubModuleState)));
        }

        public void StartAiming()
        {
            ragdollAiming.enabled = true;
            ragdollAiming.weight = 1;
        }

        public void StopAiming()
        {
            ragdollAiming.enabled = false;
            ragdollAiming.weight = 0;
        }

        public void AimingLogic(float aimInputX, float aimInputY)
        {
            throw new NotImplementedException();
        }

        public void SubscribeToInput(bool inputAction)
        {
            IsAiming = inputAction;
        }

        public void HandleInput(Dictionary<CharacterParameter, object> inputValue)
        {
            if (inputValue.TryGetValue(CharacterParameter.IsAiming, out var _isAiming))
            {
                IsAiming = (bool)_isAiming;
            }

            if (inputValue.TryGetValue(CharacterParameter.AutoAiming, out var _autoAiming))
            {
                autoAiming = (bool)_autoAiming;
            }
        }

        /// <summary>
        /// Updates the aim target position based on detected targets
        /// Handles the broadcasting of OnAimingOnTargetEvent and OnUnAimingTargetEvent
        /// </summary>
        private void UpdateAimTargetPosition()
        {
            if ((!m_targetDetected || m_aimTarget == null) && !m_hasCachedTarget) return;

            Vector3 targetPosition = m_hasCachedTarget ? m_cachedCurrentTargetPosition : m_aimTarget.position;
            Vector3 directionToTarget = (targetPosition - aimHelperTransform.position).normalized;
            float distanceToTarget = Vector3.Distance(aimHelperTransform.position, targetPosition);

            // Check if target is behind player (outside FOV)
            bool isTargetBehind =
                m_hasCachedTarget ? !m_isInFOV : IsAnyTargetBehindPlayer(playerTransform, targetPosition);
            m_isAnyTargetBehind = isTargetBehind;
            m_characterParameters.SetTargetBehindFlag(isTargetBehind);

            bool wasAiming = IsAiming;

            // If we don't allow non-FOV targets and the target is behind, don't aim at it
            if (isTargetBehind && aimingConfig != null && !aimingConfig.allowNonFOVTargets)
            {
                // No valid target to aim at (target is outside FOV and we don't allow that)
                ragdollAiming.target = null;
                m_lookAtIK.solver.target = null;
                IsAiming = false;

                if (aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log(
                        $"[AimingModule] Target outside FOV, ignoring. Position: {targetPosition}",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // Only broadcast if we were previously aiming
                if (wasAiming)
                {
                    EventManager.Broadcast(new OnUnAimingTargetEvent());
                }

                return;
            }

            // Always update the aim target position with smooth interpolation for more natural aiming
            if (m_hasCachedTarget)
            {
                // Use DOTween for smoother target position updates
                if (m_targetTween != null && m_targetTween.IsActive())
                {
                    m_targetTween.Kill();
                }

                // Faster interpolation for closer targets, slower for distant ones
                float tweenDuration = aimingConfig != null
                    ? Mathf.Lerp(0.05f, aimingConfig.targetInterpolationSpeed,
                        Mathf.Clamp01(distanceToTarget / aimingConfig.baseDistanceThreshold))
                    : 0.15f;

                m_targetTween = m_aimTarget.DOMove(m_cachedCurrentTargetPosition, tweenDuration)
                    .SetEase(Ease.OutQuad);
            }

            // Set aiming state based on whether we have a valid target
            bool isValidTarget = false;

            if (m_hasCachedTarget)
            {
                // For cached targets, check if it's in FOV or if we allow non-FOV targets
                isValidTarget = m_isInFOV || (aimingConfig != null && aimingConfig.allowNonFOVTargets);

                // Detailed logging for cached target validation
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log($"[AimingModule] Cached target validation: IsInFOV={m_isInFOV}, " +
                        $"AllowNonFOV={aimingConfig.allowNonFOVTargets}, IsValid={isValidTarget}",
                        DebugLogSettings.LogType.PlayerAiming);
                }
            }
            else
            {
                // For non-cached targets, check if it's within range and angle constraints
                bool isInCone = Vector3.Angle(aimHelperTransform.forward, directionToTarget) <= currentConeAngle;
                bool isInRange = distanceToTarget <= coneRange;

                isValidTarget = isInRange &&
                                (isInCone || (isTargetBehind && aimingConfig != null &&
                                              aimingConfig.allowNonFOVTargets));

                // Detailed logging for non-cached target validation
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log($"[AimingModule] Non-cached target validation: IsInCone={isInCone}, " +
                        $"IsInRange={isInRange}, IsTargetBehind={isTargetBehind}, " +
                        $"AllowNonFOV={aimingConfig.allowNonFOVTargets}, IsValid={isValidTarget}",
                        DebugLogSettings.LogType.PlayerAiming);
                }
            }

            if (isValidTarget)
            {
                // Set the target for aiming
                ragdollAiming.target = m_aimTarget;
                m_lookAtIK.solver.target = m_aimTarget;
                IsAiming = true;
                m_lastTarget = m_hasCachedTarget ? m_cachedCurrentTargetPosition : m_target.Value;

                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log(
                        $"[AimingModule] Aiming at target. Position: {m_lastTarget}, InFOV: {!isTargetBehind}",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // Only broadcast if we weren't already aiming or if the target changed significantly
                if (!wasAiming || !AreFloat3Equal(m_lastTarget, targetPosition))
                {
                    EventManager.Broadcast(new OnAimingOnTargetEvent(m_lastTarget));
                }

                // If target is behind and we allow non-FOV targets, trigger fast rotation
                if (isTargetBehind && aimingConfig != null && aimingConfig.allowNonFOVTargets)
                {
                    EventManager.Broadcast(new OnRequireFastRotationEvent(targetPosition));
                }
            }
            else
            {
                // No valid target to aim at
                ragdollAiming.target = null;
                m_lookAtIK.solver.target = null;
                IsAiming = false;

                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log("[AimingModule] No valid target to aim at",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // Only broadcast if we were previously aiming
                if (wasAiming)
                {
                    EventManager.Broadcast(new OnUnAimingTargetEvent());
                }
            }
        }

        public Type GetSubModuleState()
        {
            return SubModuleState.GetType();
        }

        /// <summary>
        /// Updates the detection cache with data from the ECS system
        /// Handles the broadcasting of OnDetectTargetEvent, OnLostTargetEvent, and OnSwitchTargetEvent
        /// </summary>
        private void UpdateDetectionCache()
        {
            if (detectionTarget == null || ecsWorld == null || !ecsWorld.IsCreated)
            {
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log("[AimingModule] UpdateDetectionCache: ECS system not available",
                        DebugLogSettings.LogType.PlayerAiming);
                }
                return;
            }

            // Cache previous target state before updating
            bool previousHasTarget = m_hasCachedTarget;
            float3? previousTarget = m_target;

            // Get current target from ECS
            var (currentTarget, currentPosition, hasTarget, isInFOV, lastKnownDistance, score) =
                detectionTarget.GetCurrentTarget();

            // Detailed logging for FOV detection debugging
            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log($"[AimingModule] ECS Target Data: HasTarget={hasTarget}, IsInFOV={isInFOV}, Position={currentPosition}, Score={score}",
                    DebugLogSettings.LogType.PlayerAiming);
            }

            // Update cached enemies FIRST to ensure we have the latest data for target switching
            m_cachedDetectedEnemies.Clear();
            var enemies = detectionTarget.GetDetectedEnemies(playerTransform.position);

            // Check if we've lost all enemies
            if (previousHasTarget && enemies.Length == 0)
            {
                // We've lost all enemies, make sure to un-aim
                m_targetDetected = false;
                m_target = null;
                m_hasCachedTarget = false;
                m_aimTarget.parent = playerTransform;
                m_aimTarget.position = m_forwardAimTarget.position;
                m_isForwardAiming = true;

                if (IsAiming)
                {
                    IsAiming = false;
                    // Broadcast lost target event first, then un-aiming event
                    EventManager.Broadcast(new OnLostTargetEvent());
                    EventManager.Broadcast(new OnAllLostTargetEvent());
                    EventManager.Broadcast(new OnUnAimingTargetEvent());

                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        DebugLogManager.Instance.Log("[AimingModule] All enemies lost, un-aiming",
                            DebugLogSettings.LogType.PlayerAiming);
                    }

                    float cooldownDuration = aimingConfig != null ? aimingConfig.CooldownAfterLostAllEnenies : 1.0f;
                    _cooldownEndTime = Time.time + cooldownDuration;
                    _isInCooldown = true;
                    CanUpdate = false;
                }

                return;
            }

            // Check if we need to find a new target (lost current target but still have other enemies)
            bool needsNewTarget = previousHasTarget && !hasTarget && enemies.Length > 0;

            if (needsNewTarget)
            {
                // Broadcast that we lost the current target
                EventManager.Broadcast(new OnLostTargetEvent());

                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log(
                        $"[AimingModule] Target lost but {enemies.Length} other targets available. Finding best target...",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // Find the best new target
                var bestTarget = FindBestTarget(enemies);
                if (bestTarget != null)
                {
                    // Update with new best target
                    hasTarget = true;
                    currentPosition = bestTarget.Value.Position;
                    isInFOV = bestTarget.Value.IsInFOV;
                    score = bestTarget.Value.Score;

                    // Broadcast that we switched targets
                    EventManager.Broadcast(new OnSwitchTargetEvent(currentPosition));
                    // Broadcast that we detected a new target
                    EventManager.Broadcast(new OnDetectTargetEvent(currentPosition));

                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        DebugLogManager.Instance.Log(
                            $"[AimingModule] Switched to new target at {currentPosition}, InFOV: {isInFOV}",
                            DebugLogSettings.LogType.PlayerAiming);
                    }
                }
            }

            // Update cached target information
            m_hasCachedTarget = hasTarget;
            if (hasTarget)
            {
                bool isNewTarget = !previousHasTarget ||
                                   (previousTarget.HasValue && !AreFloat3Equal(previousTarget.Value, currentPosition));

                m_cachedCurrentTargetPosition = currentPosition;
                m_target = currentPosition;
                m_lastTarget = currentPosition;
                m_isInFOV = isInFOV;
                m_currentScore = score;

                // Only broadcast detect target event if this is a new target
                if (isNewTarget)
                {
                    EventManager.Broadcast(new OnDetectTargetEvent(currentPosition));
                }

                // We'll broadcast OnAimingOnTargetEvent in UpdateAimTargetPosition to avoid duplicates
            }

            // Detailed logging for enemy detection
            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log($"[AimingModule] Detected {enemies?.Length ?? 0} enemies from ECS",
                    DebugLogSettings.LogType.PlayerAiming);
            }

            if (enemies != null)
            {
                for (int i = 0; i < enemies.Length; i++)
                {
                    var enemy = enemies[i];

                    // Manual FOV validation for debugging
                    Vector3 directionToEnemy = (enemy.Position - playerTransform.position).normalized;
                    float angleToEnemy = Vector3.Angle(playerTransform.forward, directionToEnemy);
                    float distanceToEnemy = Vector3.Distance(playerTransform.position, enemy.Position);
                    bool manualFOVCheck = angleToEnemy <= currentConeAngle * 0.5f && distanceToEnemy <= coneRange;

                    // Detailed logging for each enemy
                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        DebugLogManager.Instance.Log($"[AimingModule] Enemy {i}: Pos={enemy.Position}, " +
                            $"ECS_IsInFOV={enemy.IsInFOV}, Manual_IsInFOV={manualFOVCheck}, " +
                            $"Angle={angleToEnemy:F1}°, Distance={distanceToEnemy:F1}m, " +
                            $"ConeAngle={currentConeAngle:F1}°, ConeRange={coneRange:F1}m, Score={enemy.Score:F2}",
                            DebugLogSettings.LogType.PlayerAiming);
                    }

                    m_cachedDetectedEnemies.Add(new CachedEnemy
                    {
                        Position = enemy.Position,
                        Distance = enemy.Distance,
                        IsInFOV = enemy.IsInFOV, // Use ECS value but log discrepancies
                        Score = enemy.Score,
                        ThresholdDistanceToCurrentTartget = ThresholdDistanceToCurrentTarget,
                        ThresholdAngleToCurrentTartget = ThresholdAngleToCurrentTarget,
                    });

                    // Log discrepancies between ECS and manual FOV calculation
                    if (aimingConfig != null && aimingConfig.showTargetDebug && enemy.IsInFOV != manualFOVCheck)
                    {
                        DebugLogManager.Instance.Log($"[AimingModule] ⚠️ FOV MISMATCH for Enemy {i}: ECS={enemy.IsInFOV}, Manual={manualFOVCheck}",
                            DebugLogSettings.LogType.PlayerAiming);
                    }
                }
            }
        }

        private void onLostTargetECS()
        {
            if (m_cachedDetectedEnemies.Count == 0)
            {
                // Truly no targets left
                m_targetDetected = false;
                m_target = null;
                m_aimTarget.parent = playerTransform;
                m_aimTarget.position = m_forwardAimTarget.position;
                m_isForwardAiming = true;

                // Set cooldown timer when all enemies are lost
                float currentTime = Time.time;
                float cooldownDuration = aimingConfig != null ? aimingConfig.CooldownAfterLostAllEnenies : 1.0f;
                _cooldownEndTime = currentTime + cooldownDuration; // Calculate exact end time
                _isInCooldown = true;
                CanUpdate = false;
                
                // Debug log to confirm cooldown start time
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    Debug.Log($"[AimingModule] onLostTargetECS: Starting cooldown at time {currentTime:F2} for {cooldownDuration:F2}s. Will end at {_cooldownEndTime:F2}");
                }

                EventManager.Broadcast(new OnLostTargetEvent());
                EventManager.Broadcast(new OnUnAimingTargetEvent());

                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log($"[AimingModule] No enemies remaining, returning to forward aiming. Starting cooldown for {aimingConfig.CooldownAfterLostAllEnenies}s",
                        DebugLogSettings.LogType.PlayerAiming);
                }
            }
            else
            {
                // We have other targets but the current one was lost
                // Try to find and switch to the best available target
                var bestTarget = FindBestTarget(m_cachedDetectedEnemies.Select(ce => new DetectedEnemy
                {
                    Position = ce.Position,
                    Distance = ce.Distance,
                    IsInFOV = ce.IsInFOV,
                    Score = ce.Score
                }).ToArray());

                if (bestTarget != null)
                {
                    // Found a new target to switch to
                    m_targetDetected = true;
                    m_target = bestTarget.Value.Position;
                    m_cachedCurrentTargetPosition = bestTarget.Value.Position;
                    m_isInFOV = bestTarget.Value.IsInFOV;
                    m_hasCachedTarget = true;

                    // Immediately update the aim target to prevent delay
                    UpdateTarget(bestTarget.Value.Position);

                    // Broadcast that we're switching targets, not completely losing target
                    EventManager.Broadcast(new OnSwitchTargetEvent(bestTarget.Value.Position));

                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        DebugLogManager.Instance.Log(
                            $"[AimingModule] Switched to new target at {bestTarget.Value.Position}, InFOV: {bestTarget.Value.IsInFOV}",
                            DebugLogSettings.LogType.PlayerAiming);
                    }
                }
                else
                {
                    // Failed to find a suitable target among the remaining enemies
                    EventManager.Broadcast(new OnLostTargetEvent());
                }
            }
        }

        private void Awake()
        {
            // Load configuration if not set (following Rule #4)
            if (aimingConfig == null)
            {
                aimingConfig = Resources.Load<AimingConfiguration>("DefaultAimingConfiguration");
                if (aimingConfig == null)
                {
                    DebugLogManager.Instance.LogWarning(
                        "[AimingModule] No configuration found, creating default configuration");
                    aimingConfig = ScriptableObject.CreateInstance<AimingConfiguration>();
                }
            }

            // Initialize cone parameters from configuration
            currentConeAngle = aimingConfig.coneAngleBeforeAiming;
            coneRange = aimingConfig.coneRange;

            // Initialize ECS world
            ecsWorld = World.DefaultGameObjectInjectionWorld;
            if (ecsWorld != null)
            {
                var entityManager = ecsWorld.EntityManager;

                // Setup queries
                detectedQuery = entityManager.CreateEntityQuery(
                    ComponentType.ReadOnly<DetectedTag>(),
                    ComponentType.ReadOnly<LocalToWorld>());

                inFOVQuery = entityManager.CreateEntityQuery(
                    ComponentType.ReadOnly<InFOVTag>(),
                    ComponentType.ReadOnly<LocalToWorld>());

                currentTargetQuery = entityManager.CreateEntityQuery(
                    ComponentType.ReadOnly<CurrentTargetTag>(),
                    ComponentType.ReadOnly<LocalToWorld>());
            }

            if (detectionTarget == null)
                detectionTarget = gameObject.AddComponent<DetectionTargetSingleton>();

            // Log configuration for debugging
            if (aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log(
                    $"[AimingModule] Initialized with configuration: coneAngle={currentConeAngle}, coneRange={coneRange}, allowNonFOVTargets={aimingConfig.allowNonFOVTargets}",
                    DebugLogSettings.LogType.PlayerAiming);
            }
        }

        /// <summary>
        /// Updates the ECS detection parameters from the AimingModule (source of truth)
        /// Following Rule #2 - Event-Driven Communication
        /// </summary>
        private void UpdateECSDetectionParameters()
        {
            if (aimingConfig == null) return;

            // Broadcast event to update ECS systems
            EventManager.Broadcast(new Events.OnUpdateDetectionParametersEvent(
                currentConeAngle,
                aimingConfig.coneRange,
                aimingConfig.detectionRadius,
                aimingConfig.allowNonFOVTargets,
                aimingConfig.TargetSwitchDistanceThreshold,
                aimingConfig.MinTargetSwitchDistance,
                aimingConfig.TargetSwitchAngleThreshold,
                aimingConfig.TargetSwitchScoreThreshold,
                aimingConfig.dynamicFOVExpansion,
                aimingConfig.stickyAngle,
                aimingConfig.stickyDistance,
                aimingConfig.aimAssistSpeed));

            if (aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log(
                    $"[AimingModule] Broadcasting detection parameter update: angle={currentConeAngle}, range={coneRange}, radius={aimingConfig.detectionRadius}, allowNonFOVTargets={aimingConfig.allowNonFOVTargets}",
                    DebugLogSettings.LogType.PlayerAiming);
            }
        }

        private void OnDestroy()
        {
            if (m_targetTween != null)
            {
                m_targetTween.Kill();
                m_targetTween = null;
            }

            if (ecsWorld != null && ecsWorld.IsCreated)
            {
                detectedQuery.Dispose();
                inFOVQuery.Dispose();
                currentTargetQuery.Dispose();
            }
        }

        public float3? GetCurrentTarget()
        {
            return m_target;
        }

        public float3? GetLastTarget()
        {
            return m_lastTarget;
        }

        /// <summary>
        /// Callback when a target is detected by the sensor
        /// Handles the broadcasting of OnDetectTargetEvent
        /// </summary>
        private void onDetectTarget(Transform target, Vector3 center)
        {
            if (!m_canSwitchTarget)
                return;

            // Check if the target is inside the FOV cone (relative to player's front)
            Vector3 directionToTarget = (target.position - playerTransform.position).normalized;
            float angleToTarget = Vector3.Angle(playerTransform.forward, directionToTarget);
            float distanceToTarget = Vector3.Distance(playerTransform.position, target.position);
            bool isInFOV = angleToTarget <= currentConeAngle * 0.5f && distanceToTarget <= coneRange;

            // Check if the target is behind the player
            bool isTargetBehind = IsAnyTargetBehindPlayer(playerTransform, target.position);

            // Determine if we should process this target
            bool shouldProcess;

            if (aimingConfig.allowNonFOVTargets)
            {
                // If we allow non-FOV targets, process if:
                // 1. Target is in FOV, or
                // 2. Target is behind but is the current target, or
                // 3. Target is behind (but we allow non-FOV targets)
                shouldProcess = isInFOV || (m_targetName == target.name) || isTargetBehind;
            }
            else
            {
                // If we don't allow non-FOV targets, only process if:
                // 1. Target is in FOV
                shouldProcess = isInFOV;
            }

            if (!shouldProcess)
            {
                if (aimingConfig.showTargetDebug && isTargetBehind && !aimingConfig.allowNonFOVTargets)
                {
                    DebugLogManager.Instance.Log(
                        $"[AimingModule] Target outside FOV, ignoring. Name: {target.name}, Position: {target.position}",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                return;
            }

            // Check if this is a new target or an existing one
            bool isNewTarget = !m_allDetectedTarget.ContainsKey(target.name);

            // Add to detected targets
            m_allDetectedTarget.TryAdd(target.name, center);
            m_targetDetected = true;
            m_target = target.position;
            m_targetName = target.name;
            m_aimTarget.parent = null;
            m_isAnyTargetBehind = isTargetBehind;
            m_characterParameters.SetTargetBehindFlag(isTargetBehind);

            // Update target position
            float3? targetPosition = UpdateTarget(m_target.Value);
            if (!targetPosition.HasValue)
                return;

            m_isForwardAiming = false;

            if (aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log(
                    $"[AimingModule] Target detected. Name: {target.name}, Position: {targetPosition.Value}, InFOV: {!isTargetBehind}",
                    DebugLogSettings.LogType.PlayerAiming);
            }

            // Only broadcast detect event if this is a new target
            if (isNewTarget)
            {
                EventManager.Broadcast(new OnDetectTargetEvent(targetPosition.Value));
            }

            // If the target is behind the player and we allow non-FOV targets, trigger fast rotation
            if (isTargetBehind && aimingConfig.allowNonFOVTargets)
            {
                EventManager.Broadcast(new OnRequireFastRotationEvent(target.position));
            }
        }

        /// <summary>
        /// Callback when a target is lost by the sensor
        /// Handles the broadcasting of OnLostTargetEvent and OnUnAimingTargetEvent
        /// </summary>
        private void onLostTarget(Transform target)
        {
            // Check if this target was actually in our detected targets list
            if (!m_allDetectedTarget.Remove(target.name))
                return;

            // Log the lost target
            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log(
                    $"[AimingModule] Target lost: {target.name}", DebugLogSettings.LogType.PlayerAiming);
            }

            // Always broadcast the lost target event
            EventManager.Broadcast(new OnLostTargetEvent());

            // If this was the last target, reset to forward aiming
            if (m_allDetectedTarget.Count == 0)
            {
                m_targetDetected = false;
                m_target = null;
                m_aimTarget.parent = playerTransform;
                m_aimTarget.position = m_forwardAimTarget.position;
                m_isForwardAiming = true;

                // Set cooldown timer when all enemies are lost
                float currentTime = Time.time;
                float cooldownDuration = aimingConfig != null ? aimingConfig.CooldownAfterLostAllEnenies : 1.0f;
                _cooldownEndTime = currentTime + cooldownDuration; // Calculate exact end time
                _isInCooldown = true;
                CanUpdate = false;
                
                // Debug log to confirm cooldown start time
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    Debug.Log($"[AimingModule] onLostTarget: Starting cooldown at time {currentTime:F2} for {cooldownDuration:F2}s. Will end at {_cooldownEndTime:F2}");
                }

                // Only broadcast un-aiming if we were actually aiming
                if (IsAiming)
                {
                    IsAiming = false;
                    EventManager.Broadcast(new OnUnAimingTargetEvent());

                    if (aimingConfig != null && aimingConfig.showTargetDebug)
                    {
                        DebugLogManager.Instance.Log(
                            $"[AimingModule] No targets remaining, un-aiming. Starting cooldown for {aimingConfig.CooldownAfterLostAllEnenies}s",
                            DebugLogSettings.LogType.PlayerAiming);
                    }
                }
            }
            else
            {
                // We still have other targets, try to find the best one to switch to
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log(
                        $"[AimingModule] Target {target.name} lost, but {m_allDetectedTarget.Count} targets remain. Finding best target...",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // The actual target switching will be handled in UpdateDetectionCache
            }
        }

        private bool IsAnyTargetBehindPlayer(Transform playerTransform, float3 targetPosition)
        {
            // Check if the target is behind the player by calculating the dot product
            // between the player's forward direction and the direction to the target
            Vector3 directionToTarget = targetPosition - (float3)playerTransform.position;
            directionToTarget.Normalize();

            // Dot product < 0 means the angle is > 90 degrees, which means the target is behind the player
            float dotProduct = Vector3.Dot(playerTransform.forward, directionToTarget);

            // Return true if the target is behind the player (dot product < 0)
            return dotProduct < 0;
        }
#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            // Always draw FOV relative to the player's forward direction
            if (playerTransform == null) return;

            // Draw FOV cone
            Gizmos.color = Color.green; // Green for FOV cone (valid targeting area)

            Vector3 origin = playerTransform.position;
            Vector3 forward = playerTransform.forward * coneRange;
            Vector3 leftBoundary = Quaternion.Euler(0, -currentConeAngle / 2, 0) * forward;
            Vector3 rightBoundary = Quaternion.Euler(0, currentConeAngle / 2, 0) * forward;

            Gizmos.DrawLine(origin, origin + forward);
            Gizmos.DrawLine(origin, origin + leftBoundary);
            Gizmos.DrawLine(origin, origin + rightBoundary);

            // Draw the arc of the cone
            int segments = 20;
            float segmentAngle = currentConeAngle / segments;
            Vector3 prevPoint = origin + leftBoundary;
            for (int i = 1; i <= segments; i++)
            {
                Vector3 nextBoundary = Quaternion.Euler(0, -currentConeAngle / 2 + segmentAngle * i, 0) * forward;
                Vector3 nextPoint = origin + nextBoundary;
                Gizmos.DrawLine(prevPoint, nextPoint);
                prevPoint = nextPoint;
            }

            // Draw a semi-transparent disc to represent the FOV area
            Handles.color = new Color(0, 1, 0, 0.1f); // Semi-transparent green
            Handles.DrawSolidArc(origin, Vector3.up,
                Quaternion.Euler(0, -currentConeAngle / 2, 0) * playerTransform.forward,
                currentConeAngle, coneRange);

            // If we allow non-FOV targets, draw the behind area in a different color
            if (aimingConfig.allowNonFOVTargets)
            {
                // Draw the behind area in red (semi-transparent)
                Handles.color = new Color(1, 0, 0, 0.05f); // Semi-transparent red
                Handles.DrawSolidArc(origin, Vector3.up,
                    Quaternion.Euler(0, currentConeAngle / 2, 0) * playerTransform.forward,
                    360 - currentConeAngle, coneRange);
            }

            // Draw current target if available
            if (m_target.HasValue)
            {
                // Draw a sphere at the target position
                Gizmos.color = m_isInFOV ? Color.green : Color.red;
                Gizmos.DrawSphere(m_target.Value, 0.3f);

                // Draw a line from player to target
                Gizmos.DrawLine(origin, m_target.Value);

                // Label the target
                Handles.Label(m_target.Value * math.up() + 0.5f,
                    $"Target: {m_targetName}\nIn FOV: {m_isInFOV}\nDistance: {math.distance(origin, m_target.Value):F1}m");
            }

            // Draw all cached enemies
            foreach (var enemy in m_cachedDetectedEnemies)
            {
                // Use different colors for FOV and non-FOV enemies
                Gizmos.color = enemy.IsInFOV ? new Color(0, 0.8f, 0, 0.5f) : new Color(0.8f, 0, 0, 0.5f);
                Gizmos.DrawSphere(enemy.Position, 0.2f);
            }
        }
#endif

        public void OnSubModuleChange(WeaponSubModuleState subModule)
        {
            if (subModule == WeaponSubModuleState.EmptyHand)
            {
                return;
            }

            ragdollAiming.SetWeaponData(SubModules[subModule].AimingData);
        }

        private bool AreFloat3Equal(float3 a, float3 b, float epsilon = 0.1f)
        {
            return math.abs(a.x - b.x) < epsilon &&
                   math.abs(a.y - b.y) < epsilon &&
                   math.abs(a.z - b.z) < epsilon;
        }

        public float3? UpdateTarget(float3 targetPosition)
        {
            if (DOTween.IsTweening("onDetectTarget"))
            {
                DOTween.Kill("onDetectTarget");
            }

            if (!m_hasCachedTarget || m_cachedDetectedEnemies.Count == 0)
                return null;

            // Get current target distance
            float currentTargetDistance = math.distance(m_cachedCurrentTargetPosition, playerTransform.position);
            float3 closestPosition = m_cachedCurrentTargetPosition;

            // Calculate tween duration - faster for closer targets, slower for distant ones
            float tweenDuration = aimingConfig != null
                ? Mathf.Lerp(0.05f, aimingConfig.targetInterpolationSpeed,
                    Mathf.Clamp01(currentTargetDistance / aimingConfig.baseDistanceThreshold))
                : 0.15f;

            // When switching targets, use even faster tweening to reduce delay
            if (m_isSwitchingTarget)
            {
                tweenDuration *= 0.5f; // Half the duration when switching targets
                m_isSwitchingTarget = false;
            }

            // Update aim target position with tweening
            if (m_targetTween == null)
            {
                m_targetTween = m_aimTarget.DOMove(closestPosition, tweenDuration)
                    .SetAutoKill(false)
                    .SetEase(Ease.OutQuad)
                    .SetTarget("onDetectTarget");
            }
            else
            {
                m_targetTween.ChangeEndValue(closestPosition, true);
            }

            m_targetTween.OnComplete(() => m_aimTarget.position = targetPosition);

            m_lastTarget = closestPosition;
            return closestPosition;
        }

        /// <summary>
        /// Updates the aiming state based on the IsAiming property
        /// This is called whenever the IsAiming property changes
        /// </summary>
        private void UpdateAimingState()
        {
            if (IsAiming)
            {
                SubState = WeaponSubState.Aiming;
                ragdollAiming.enabled = true;
                m_lookAtIK.solver.IKPositionWeight = 1f;
                DOTween.To(() => currentConeAngle, x => currentConeAngle = x, aimingConfig.coneAngleAfterAiming, 1)
                    .OnUpdate(()=> EventManager.Broadcast(new OnChangeConeAngleEvent(){DetectionAngle =currentConeAngle}));
                
                // We don't broadcast OnAimingOnTargetEvent here because it's handled in UpdateAimTargetPosition
                // with the correct target position
            }
            else
            {
                SubState = WeaponSubState.Idle;
                ragdollAiming.enabled = false;
                m_lookAtIK.solver.IKPositionWeight = 0f;
                DOTween.To(() => currentConeAngle, x => currentConeAngle = x, aimingConfig.coneAngleBeforeAiming, 1)
                    .OnUpdate(()=> EventManager.Broadcast(new OnChangeConeAngleEvent(){DetectionAngle =currentConeAngle}));
                UpdateECSDetectionParameters();
                // We don't broadcast OnUnAimingTargetEvent here because it's handled in UpdateAimTargetPosition
                // and onLostTarget methods to avoid duplicate events
            }

            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log(
                    $"[AimingModule] Aiming state updated: {(IsAiming ? "Aiming" : "Not Aiming")}",
                    DebugLogSettings.LogType.PlayerAiming);
            }
        }

        /// <summary>
        /// Finds the best target from a list of detected enemies based on scoring
        /// Used when switching targets after losing the current target
        ///
        /// Prioritizes targets in this order:
        /// 1. Targets in FOV (in front of the player)
        /// 2. Closest targets
        /// 3. Targets with highest overall score (combination of distance and angle)
        /// </summary>
        /// <param name="enemies">Array of detected enemies to choose from</param>
        /// <returns>The best target or null if no suitable target found</returns>
        private DetectedEnemy? FindBestTarget(DetectedEnemy[] enemies)
        {
            if (enemies == null || enemies.Length == 0)
            {
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log("[AimingModule] FindBestTarget: No enemies provided",
                        DebugLogSettings.LogType.PlayerAiming);
                }
                return null;
            }

            // Detailed logging for target selection
            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log($"[AimingModule] FindBestTarget: Evaluating {enemies.Length} enemies",
                    DebugLogSettings.LogType.PlayerAiming);

                for (int i = 0; i < enemies.Length; i++)
                {
                    var enemy = enemies[i];
                    DebugLogManager.Instance.Log($"[AimingModule] Enemy {i}: IsInFOV={enemy.IsInFOV}, Distance={enemy.Distance:F1}m, Score={enemy.Score:F2}",
                        DebugLogSettings.LogType.PlayerAiming);
                }
            }

            // Trust ECS scoring - enemies are already sorted by OptimizedDetectionSystem
            // Just prioritize FOV targets if available, then use ECS score order
            var sortedEnemies = enemies
                .OrderByDescending(e => e.IsInFOV) // Prioritize targets in FOV
                .ThenByDescending(e => e.Score) // Then by ECS calculated score (higher = better)
                .ToArray();

            if (sortedEnemies.Length > 0)
            {
                var bestTarget = sortedEnemies[0];

                // Detailed logging for selected target
                if (aimingConfig != null && aimingConfig.showTargetDebug)
                {
                    DebugLogManager.Instance.Log($"[AimingModule] Selected best target: IsInFOV={bestTarget.IsInFOV}, Distance={bestTarget.Distance:F1}m, Score={bestTarget.Score:F2}",
                        DebugLogSettings.LogType.PlayerAiming);
                }

                // Flag that we're switching targets to optimize tweening
                m_isSwitchingTarget = true;
                return bestTarget;
            }

            if (aimingConfig != null && aimingConfig.showTargetDebug)
            {
                DebugLogManager.Instance.Log("[AimingModule] FindBestTarget: No suitable target found after sorting",
                    DebugLogSettings.LogType.PlayerAiming);
            }

            return null;
        }
    }
}