using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;

public enum DetectionMethod
{
    SphereCast = 0,
    KDTree = 1,
    Hybrid = 2
}

public struct SphereDetectSensorComponent : IComponentData
{
    public bool CanDetect;
    public bool CanLose;
    public float CooldownTime;
    public float3 LastPosition;
    public int EntityIndex;
    public Entity LastDetectedEntity; // Track the last detected
    public float3 SensorPosition;

    public float DetectionRadius; // Max radius for losing detection (used in lost check)
    public float DetectionRange;  // Max range for detecting enemies (used in aiming)
    public float DetectionAngle;  // Field of View angle (degrees). Larger = wider FOV, more unstable; smaller = narrower, more stable aim.

    public uint BelongsTo;
    public uint CollidesWith;
    public float CheckInterval; // How often to check for targets (seconds)
    public float LastCheckTime;
    public Entity ClosestOutFOVTarget;
    public bool UseKDTree; // Flag to enable KDTree-based detection (deprecated - use DetectionMethod instead)
    public DetectionMethod DetectionMethod; // Detection method to use

    // --- AIMING STABILITY TUNING PARAMETERS ---
    // Lower values make target switching less likely (more stable aim)
    // Higher values make aiming more responsive, but less stable

    public float TargetSwitchDistanceThreshold; // How much closer a new target needs to be (as a percentage) to trigger a switch. Lower = more stable aim
    public float MinTargetSwitchDistance; // Minimum distance difference required to switch targets (prevents rapid switching)
    public float TargetSwitchAngleThreshold; // Maximum angle difference allowed for target switching. Lower = more stable aim
    public float TargetSwitchScoreThreshold; // Minimum score difference required to switch targets (0.2f means new target must have 20% higher score). Higher = more stable aim
    public float LastTargetSwitchTime; // Time when the last target switch occurred
    
    // --- AAA AIM ASSIST PARAMETERS ---
    public float dynamicFOVExpansion; // Degrees to temporarily expand FOV when a target is near the edge
    public float stickyAngle;         // Angle in degrees for sticky targeting
    public float stickyDistance;      // Distance in meters for sticky targeting
    public float aimAssistSpeed;      // Degrees per second for auto-rotation toward target
    // --- END AAA AIM ASSIST PARAMETERS ---
}

public struct DetectionResult:IComponentData
{
    public Entity DetectorEntity;
    public Entity DetectedEntity;
    public int EntityIndex;
    public float3 LastPosition;
}

public struct DetectedTag : IComponentData
{
}
public struct LostTag : IComponentData {}