using Module.Mono.Animancer.RealsticFemale;
using PlayerFAP.Authorings;
using PlayerFAP.Components.Detection;
using PlayerFAP.Tags;
using Sirenix.OdinInspector;
using Unity.Collections;
using Unity.Entities;
using Unity.Physics;
using Unity.Physics.Authoring;
using UnityEngine;

public class AimModuleAuthoring : SerializedMonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private AimingConfiguration aimingConfig;

    [<PERSON><PERSON>("Detection Settings")]
    [Tooltip("Detection method to use for enemy detection")]
    public DetectionMethod detectionMethod = DetectionMethod.Hybrid;

    [Tooltip("Legacy KDTree setting - use Detection Method instead")]
    public bool EnableKDTree;
    public float cooldownTime = 1.0f;
    public bool canDetect = true;
    public bool canLose = true;
    public PhysicsCategoryTags BelongsTo;
    public PhysicsCategoryTags CollideWith;

    [Tooltip("Minimum time to keep the same target (seconds)")]
    [Range(0.1f, 2.0f)]
    public float MinTargetSwitchTime = 0.5f;

    [Head<PERSON>("Performance Settings")]
    [Tooltip("How often to check for enemies (in seconds)")]
    public float CheckInterval = 1f;

    [Header("Triad Cluster Settings")]
    [Tooltip("Enable the Triad Cluster system for improved target selection")]
    public bool EnableClustering = true;

    [Tooltip("Maximum distance between enemies to be considered part of the same cluster")]
    public float ClusteringRadius = 25.0f; // Increased to handle larger groups

    [Tooltip("Weight applied to cluster distance from player (negative values prioritize closer clusters)")]
    [Range(-1f, 1f)]
    public float ClusterScoreWeight_CentroidDistance = -0.3f; // Reduced to make distance less dominant

    [Tooltip("Weight applied to the number of enemies in a cluster (positive values prioritize larger clusters)")]
    [Range(-1f, 1f)]
    public float ClusterScoreWeight_MemberCount = 0.7f; // Increased to prioritize larger groups

    [Tooltip("Weight applied to the average score of enemies in a cluster")]
    [Range(-1f, 1f)]
    public float ClusterScoreWeight_AverageScore = 0.2f; // Slightly increased

    [Tooltip("How to select targets within the best cluster")]
    public IntraClusterTargetSelectionMode TargetSelectionMode = IntraClusterTargetSelectionMode.ClosestToCentroid; // Changed to target cluster centers

    [Tooltip("Maximum number of targets to lock onto simultaneously (1 for single target, >1 for multi-target)")]
    [Range(1, 10)]
    public int MaxSimultaneousTargets = 1;

    [Tooltip("How much better a new cluster must score to switch from the current cluster (higher = more stable targeting)")]
    [Range(0.1f, 1.0f)]
    public float ClusterHysteresisThreshold = 0.3f;

    // Configuration-based properties (read from AimingConfiguration)
    public float DetectionRadius => aimingConfig?.detectionRadius ?? 6f;
    public float DetectionRange => aimingConfig?.coneRange ?? 20f;
    public float DetectionAngle => aimingConfig?.coneAngleBeforeAiming ?? 45f;
    public float TargetSwitchDistanceThreshold => aimingConfig?.TargetSwitchDistanceThreshold ?? 0.1f;
    public float MinTargetSwitchDistance => aimingConfig?.MinTargetSwitchDistance ?? 0.1f;
    public float TargetSwitchAngleThreshold => aimingConfig?.TargetSwitchAngleThreshold ?? 15f;
    public float TargetSwitchScoreThreshold => aimingConfig?.TargetSwitchScoreThreshold ?? 0.1f;

    private void Awake()
    {
        UpdateValuesFromConfiguration();
    }

    /// <summary>
    /// Updates values from AimingConfiguration, following the established pattern
    /// </summary>
    private void UpdateValuesFromConfiguration()
    {
        if (aimingConfig == null)
        {
            aimingConfig = Resources.Load<AimingConfiguration>("AimingConfiguration");
            if (aimingConfig == null)
            {
                Debug.LogWarning("[AimModuleAuthoring] AimingConfiguration not found in Resources folder. Using default values.");
            }
        }
    }
}

class AimModuleBaker : Baker<AimModuleAuthoring>
{
    public override void Bake(AimModuleAuthoring authoring)
    {
        var entity = GetEntity(TransformUsageFlags.Dynamic);

        AddComponent<PlayerTag>(entity);

        AddComponent(entity, new SphereDetectSensorComponent
        {
            UseKDTree = authoring.EnableKDTree, // Keep for backward compatibility
            DetectionMethod = authoring.detectionMethod, // New detection method
            CooldownTime = authoring.cooldownTime,
            CanDetect = authoring.canDetect,
            CanLose = authoring.canLose,
            DetectionRadius = authoring.DetectionRadius, // Now reads from configuration
            DetectionRange = authoring.DetectionRange, // Now reads from configuration
            DetectionAngle = authoring.DetectionAngle, // Now reads from configuration
            BelongsTo = authoring.BelongsTo.Value,
            CollidesWith = authoring.CollideWith.Value,
            CheckInterval = authoring.CheckInterval,
            LastCheckTime = 0f,
            TargetSwitchDistanceThreshold = authoring.TargetSwitchDistanceThreshold, // Now reads from configuration
            MinTargetSwitchDistance = authoring.MinTargetSwitchDistance, // Now reads from configuration
            TargetSwitchAngleThreshold = authoring.TargetSwitchAngleThreshold, // Now reads from configuration
            TargetSwitchScoreThreshold = authoring.TargetSwitchScoreThreshold // Now reads from configuration
        });

        // Add TriadClusterSettingsComponent
        AddComponent(entity, new TriadClusterSettingsComponent
        {
            EnableClustering = authoring.EnableClustering,
            ClusteringRadius = authoring.ClusteringRadius,
            ClusterScoreWeight_CentroidDistance = authoring.ClusterScoreWeight_CentroidDistance,
            ClusterScoreWeight_MemberCount = authoring.ClusterScoreWeight_MemberCount,
            ClusterScoreWeight_AverageScore = authoring.ClusterScoreWeight_AverageScore,
            TargetSelectionMode = authoring.TargetSelectionMode,
            MaxSimultaneousTargets = authoring.MaxSimultaneousTargets,
            ClusterHysteresisThreshold = authoring.ClusterHysteresisThreshold
        });
    }
}

public partial struct AimModuleSensorSystem : ISystem
{
    public void OnUpdate(ref SystemState state)
    {
        var ecb = new EntityCommandBuffer(Allocator.TempJob);

        if (Object.FindFirstObjectByType<AimingModule>() == null)
        {
            return;
        }

        // Query over SphereDetectSensorComponent to perform operations
        foreach (var (sensorComponent, entity) in SystemAPI.Query<RefRW<SphereDetectSensorComponent>>()
                     .WithEntityAccess())
        {
            ecb.AddComponent(entity,
                new AimModuleGO {transform = Object.FindFirstObjectByType<AimingModule>().transform});
        }

        ecb.Playback(state.EntityManager);
        ecb.Dispose();
    }
}